==== Meshes: 1
- Mesh 'color_not_full_a' vtx:4 face:1 loop:4 edge:4
    - 0 1 2 3 
    - 0/3 0/1 1/2 2/3 
  - attr 'position' FLOAT_VECTOR POINT
    - (1.000, 0.000, 1.000)
    - (1.000, 0.000, -1.000)
    - (-1.000, 0.000, -1.000)
    - (-1.000, 0.000, 1.000)
  - attr 'sharp_face' BOOLEAN FACE
    - 1 

==== Objects: 1
- Obj 'color_not_full_a' MESH data:'color_not_full_a'
  - pos 0.000, 0.000, 0.000
  - rot 0.000, 0.000, 0.000 (XYZ)
  - scl 1.000, 1.000, 1.000

