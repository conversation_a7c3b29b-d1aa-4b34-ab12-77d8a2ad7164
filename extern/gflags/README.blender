Project: Google Flags
URL: https://github.com/gflags/gflags
License: SPDX:BSD-3-Clause
Upstream version: 2.2.1 (46f73f88b18)
Copyright: Copyright (c) 1999, Google Inc. All rights reserved.
Local modifications:

- Flattened the tree and only included files needed for Blender.

- config.h was originally generated on linux machine with some
  further tweaks:

  * OS_WINDOWS need to be conditinally defined from inside #ifdef WIN32
  * Same applies to HAVE_SHLWAPI_H
  * Disabeld HAVE_FNMATCH_H
  * Forced disabled GFLAGS_IS_A_DLL

- gflags_declare.h was modified
  * Forced disabled GFLAGS_IS_A_DLL

- Applied some modifications from fork https://github.com/Nazg-Gul/gflags.git
  (see https://github.com/gflags/gflags/pull/129)

- Ifdef-ed __attribute((unused)) in gflags.h.
  This file is compile-time configurable in upstream, so can not avoid change here.
