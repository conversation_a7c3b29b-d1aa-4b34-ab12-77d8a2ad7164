

// DO NOT EDIT !
// This file is generated using the MantaFlow preprocessor (prep link).

#include "movingobs.h"
namespace Manta {
#ifdef _C_MovingObstacle
static const Pb::Register _R_17("MovingObstacle", "MovingObstacle", "PbClass");
template<> const char *Namify<MovingObstacle>::S = "MovingObstacle";
static const Pb::Register _R_18("MovingObstacle", "MovingObstacle", MovingObstacle::_W_0);
static const Pb::Register _R_19("MovingObstacle", "add", MovingObstacle::_W_1);
static const Pb::Register _R_20("MovingObstacle", "moveLinear", MovingObstacle::_W_2);
static const Pb::Register _R_21("MovingObstacle", "projectOutside", MovingObstacle::_W_3);
#endif
extern "C" {
void PbRegister_file_17()
{
  KEEP_UNUSED(_R_17);
  KEEP_UNUSED(_R_18);
  KEEP_UNUSED(_R_19);
  KEEP_UNUSED(_R_20);
  KEEP_UNUSED(_R_21);
}
}
}  // namespace Manta