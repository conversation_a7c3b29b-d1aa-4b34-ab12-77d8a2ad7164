# UPBGE Python函数暴露修复

## 问题描述

用户报告：**"python脚本取不到bpy的C API更新物理，bpy里没那个函数"**

这表明我们的C函数虽然编译成功，但没有正确暴露到Python的bpy模块中，导致用户无法在Python脚本中访问这些函数。

## 问题分析

### 原始问题
1. **函数未正确暴露**: 函数虽然编译成功，但Python无法访问
2. **模块注册问题**: 函数没有正确添加到bpy模块的方法列表中
3. **链接方式问题**: 函数可能没有使用正确的链接方式

### 根本原因
在Blender/UPBGE中，有两种方式将C函数暴露给Python：
1. **通过bpy_methods数组**: 直接添加到主要的方法列表中
2. **通过模块添加函数**: 在运行时动态添加函数

我们之前只使用了第二种方式，但可能存在问题。

## 修复方案

### 1. 双重注册策略

我们采用两种方式同时注册函数，确保函数能够被正确暴露：

#### A. 添加到bpy_methods数组

**文件**: `source/blender/python/intern/bpy.cc`

```cpp
/* Forward declarations for our rigidbody skeletal functions */
extern PyObject *bpy_rigidbody_update_for_skeletal_animation(PyObject *self, PyObject *args, PyObject *kw);
extern PyObject *bpy_rigidbody_update_fallback(PyObject *self, PyObject *args, PyObject *kw);

/* Documentation strings */
extern const char bpy_rigidbody_update_for_skeletal_animation_doc[];
extern const char bpy_rigidbody_update_fallback_doc[];

static PyMethodDef bpy_methods[] = {
    // ... 其他函数 ...
    
    /* UPBGE: Rigidbody skeletal animation functions */
    {"update_for_skeletal_animation",
     (PyCFunction)bpy_rigidbody_update_for_skeletal_animation,
     METH_VARARGS | METH_KEYWORDS,
     bpy_rigidbody_update_for_skeletal_animation_doc},
    {"update_fallback",
     (PyCFunction)bpy_rigidbody_update_fallback,
     METH_VARARGS | METH_KEYWORDS,
     bpy_rigidbody_update_fallback_doc},
    
    // ... 其他函数 ...
    {nullptr, nullptr, 0, nullptr},
};
```

#### B. 移除static关键字

**文件**: `source/blender/python/intern/bpy_rigidbody_skeletal.cc`

```cpp
// 从这个：
static PyObject *bpy_rigidbody_update_for_skeletal_animation(...)

// 改为这个：
PyObject *bpy_rigidbody_update_for_skeletal_animation(...)
```

#### C. 更新头文件声明

**文件**: `source/blender/python/intern/bpy_rigidbody_skeletal.hh`

```cpp
#ifdef __cplusplus
extern "C" {
#endif

void BPY_rigidbody_skeletal_add_to_module(PyObject *mod);

PyObject *bpy_rigidbody_update_for_skeletal_animation(PyObject *self, PyObject *args, PyObject *kw);
PyObject *bpy_rigidbody_update_fallback(PyObject *self, PyObject *args, PyObject *kw);

extern const char bpy_rigidbody_update_for_skeletal_animation_doc[];
extern const char bpy_rigidbody_update_fallback_doc[];

#ifdef __cplusplus
}
#endif
```

### 2. 改进的模块添加函数

**文件**: `source/blender/python/intern/bpy_rigidbody_skeletal.cc`

```cpp
extern "C" void BPY_rigidbody_skeletal_add_to_module(PyObject *mod)
{
    if (!mod) {
        return;
    }
    
    /* Add functions with error checking and debugging */
    for (int i = 0; rigidbody_skeletal_methods[i].ml_name; i++) {
        PyMethodDef *m = &rigidbody_skeletal_methods[i];
        PyObject *func = PyCFunction_New(m, mod);
        if (func) {
            if (PyModule_AddObject(mod, m->ml_name, func) < 0) {
                Py_DECREF(func);
                printf("Failed to add function %s to bpy module\n", m->ml_name);
            } else {
                printf("Successfully added function %s to bpy module\n", m->ml_name);
            }
        } else {
            printf("Failed to create function %s\n", m->ml_name);
        }
    }
}
```

## 验证方法

### 1. 编译验证
```bash
cmd //c make.bat lite
```

### 2. 运行时验证

使用测试脚本 `test_bpy_functions.py`：

```python
import bpy

# 检查函数是否存在
print("update_for_skeletal_animation" in dir(bpy))
print("update_fallback" in dir(bpy))

# 尝试调用函数
try:
    bpy.update_for_skeletal_animation()
except Exception as e:
    print(f"函数存在但调用失败（预期）: {e}")
```

### 3. 控制台输出检查

启动UPBGE时，应该在控制台看到：
```
Successfully added function update_for_skeletal_animation to bpy module
Successfully added function update_fallback to bpy module
```

## 常见问题排查

### 1. 函数仍然不可见

**可能原因**:
- 编译时函数没有被包含
- 链接错误导致函数不存在
- 模块初始化顺序问题

**解决方法**:
- 检查CMakeLists.txt中的源文件列表
- 验证链接器输出
- 检查函数调用顺序

### 2. 函数存在但调用失败

**可能原因**:
- 参数解析问题
- C函数内部错误
- 依赖的C函数不存在

**解决方法**:
- 检查参数解析代码
- 验证BKE_rigidbody_update_for_skeletal_animation函数
- 添加更多错误检查

### 3. 编译错误

**可能原因**:
- 函数声明不匹配
- 头文件包含问题
- extern "C"声明错误

**解决方法**:
- 确保声明和实现一致
- 检查所有必要的头文件
- 验证C++/C链接声明

## 测试用例

### 基本功能测试
```python
import bpy

# 测试1: 检查函数存在
assert hasattr(bpy, 'update_for_skeletal_animation')
assert hasattr(bpy, 'update_fallback')

# 测试2: 检查函数类型
assert callable(bpy.update_for_skeletal_animation)
assert callable(bpy.update_fallback)

# 测试3: 检查文档字符串
assert bpy.update_for_skeletal_animation.__doc__ is not None
assert 'skeletal animation' in bpy.update_for_skeletal_animation.__doc__.lower()
```

### 实际使用测试
```python
import bpy

# 在有场景的情况下测试
try:
    # 使用当前场景
    result = bpy.update_for_skeletal_animation()
    print("主函数调用成功")
except Exception as e:
    print(f"主函数调用失败: {e}")

try:
    # 使用备用函数
    result = bpy.update_fallback()
    print("备用函数调用成功")
except Exception as e:
    print(f"备用函数调用失败: {e}")
```

## 预期结果

修复后，用户应该能够：

1. **在Python中看到函数**:
   ```python
   >>> import bpy
   >>> 'update_for_skeletal_animation' in dir(bpy)
   True
   ```

2. **调用函数**:
   ```python
   >>> bpy.update_for_skeletal_animation()
   # 成功执行或返回有意义的错误信息
   ```

3. **查看文档**:
   ```python
   >>> help(bpy.update_for_skeletal_animation)
   # 显示函数文档
   ```

## 总结

通过双重注册策略（bpy_methods数组 + 动态添加），我们确保函数能够正确暴露给Python。这种方法提供了：

1. **可靠性**: 两种注册方式确保至少一种有效
2. **调试能力**: 添加了详细的错误输出
3. **兼容性**: 遵循Blender的标准模式
4. **可维护性**: 清晰的代码结构和文档

用户现在应该能够在Python脚本中正常访问和使用我们的骨骼动画物理更新函数。 