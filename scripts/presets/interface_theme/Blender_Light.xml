<bpy>
  <Theme>
    <user_interface>
      <ThemeUserInterface
        menu_shadow_fac="0.3"
        menu_shadow_width="4"
        icon_alpha="1"
        icon_saturation="0.5"
        widget_emboss="#00000026"
        editor_border="#999999"
        editor_outline="#b3b3b3ff"
        editor_outline_active="#ccccccff"
        widget_text_cursor="#3399e6"
        panel_roundness="0.4"
        transparent_checker_primary="#333333"
        transparent_checker_secondary="#262626"
        transparent_checker_size="8"
        axis_x="#ff3352"
        axis_y="#8bdc00"
        axis_z="#2890ff"
        gizmo_hi="#ffffff"
        gizmo_primary="#f5f14d"
        gizmo_secondary="#63ffff"
        gizmo_view_align="#ffffff"
        gizmo_a="#4da84d"
        gizmo_b="#a33535"
        icon_scene="#e6e6e6ff"
        icon_collection="#f4f4f4ff"
        icon_object="#ee9e5dff"
        icon_object_data="#00d4a3ff"
        icon_modifier="#84b8ffff"
        icon_shading="#ea7581ff"
        icon_folder="#e3c16eff"
        icon_autokey="#ab3c48ff"
        icon_border_intensity="0.7"
        >
        <wcol_regular>
          <ThemeWidgetColors
            outline="#4d4d4dff"
            inner="#dbdbdbff"
            inner_sel="#668cccff"
            item="#191919ff"
            text="#1a1a1a"
            text_sel="#ffffff"
            show_shaded="FALSE"
            shadetop="0"
            shadedown="-5"
            roundness="0.4"
            >
          </ThemeWidgetColors>
        </wcol_regular>
        <wcol_tool>
          <ThemeWidgetColors
            outline="#4d4d4dff"
            inner="#dbdbdbff"
            inner_sel="#5680c2ff"
            item="#191919ff"
            text="#000000"
            text_sel="#ffffff"
            show_shaded="FALSE"
            shadetop="0"
            shadedown="-5"
            roundness="0.4"
            >
          </ThemeWidgetColors>
        </wcol_tool>
        <wcol_toolbar_item>
          <ThemeWidgetColors
            outline="#4d4d4dff"
            inner="#434343ff"
            inner_sel="#5680c2ff"
            item="#e6e6e6cc"
            text="#e6e6e6"
            text_sel="#ffffff"
            show_shaded="FALSE"
            shadetop="0"
            shadedown="0"
            roundness="0.4"
            >
          </ThemeWidgetColors>
        </wcol_toolbar_item>
        <wcol_radio>
          <ThemeWidgetColors
            outline="#4d4d4dff"
            inner="#3b3b3bff"
            inner_sel="#5680c2e6"
            item="#ffffffff"
            text="#d9d9d9"
            text_sel="#ffffff"
            show_shaded="FALSE"
            shadetop="5"
            shadedown="-5"
            roundness="0.4"
            >
          </ThemeWidgetColors>
        </wcol_radio>
        <wcol_text>
          <ThemeWidgetColors
            outline="#4d4d4dff"
            inner="#282828ff"
            inner_sel="#333333ff"
            item="#ffffff33"
            text="#dddddd"
            text_sel="#ffffff"
            show_shaded="TRUE"
            shadetop="-8"
            shadedown="0"
            roundness="0.4"
            >
          </ThemeWidgetColors>
        </wcol_text>
        <wcol_option>
          <ThemeWidgetColors
            outline="#4d4d4dff"
            inner="#3c3c3cff"
            inner_sel="#5680c2ff"
            item="#ffffffff"
            text="#1a1a1a"
            text_sel="#000000"
            show_shaded="TRUE"
            shadetop="-5"
            shadedown="0"
            roundness="0.4"
            >
          </ThemeWidgetColors>
        </wcol_option>
        <wcol_toggle>
          <ThemeWidgetColors
            outline="#4d4d4dff"
            inner="#dbdbdbff"
            inner_sel="#5680c2ff"
            item="#191919ff"
            text="#000000"
            text_sel="#ffffff"
            show_shaded="FALSE"
            shadetop="0"
            shadedown="0"
            roundness="0.4"
            >
          </ThemeWidgetColors>
        </wcol_toggle>
        <wcol_num>
          <ThemeWidgetColors
            outline="#4d4d4dff"
            inner="#d3d3d3ff"
            inner_sel="#5680c2ff"
            item="#80b1ffff"
            text="#1a1a1a"
            text_sel="#ffffff"
            show_shaded="TRUE"
            shadetop="3"
            shadedown="0"
            roundness="0.4"
            >
          </ThemeWidgetColors>
        </wcol_num>
        <wcol_numslider>
          <ThemeWidgetColors
            outline="#4d4d4dff"
            inner="#999999ff"
            inner_sel="#b0b0b0ff"
            item="#e6e6e6ff"
            text="#1a1a1a"
            text_sel="#000000"
            show_shaded="TRUE"
            shadetop="-3"
            shadedown="0"
            roundness="0.4"
            >
          </ThemeWidgetColors>
        </wcol_numslider>
        <wcol_box>
          <ThemeWidgetColors
            outline="#4d4d4dff"
            inner="#80808080"
            inner_sel="#5680c2ff"
            item="#191919ff"
            text="#333333"
            text_sel="#ffffff"
            show_shaded="FALSE"
            shadetop="0"
            shadedown="0"
            roundness="0.4"
            >
          </ThemeWidgetColors>
        </wcol_box>
        <wcol_menu>
          <ThemeWidgetColors
            outline="#3d3d3dff"
            inner="#3b3b3bff"
            inner_sel="#767676ff"
            item="#808080ff"
            text="#d9d9d9"
            text_sel="#ffffff"
            show_shaded="FALSE"
            shadetop="10"
            shadedown="-10"
            roundness="0.4"
            >
          </ThemeWidgetColors>
        </wcol_menu>
        <wcol_pulldown>
          <ThemeWidgetColors
            outline="#4d4d4dff"
            inner="#b3b3b3cc"
            inner_sel="#5680c2e6"
            item="#727272ff"
            text="#1a1a1a"
            text_sel="#ffffff"
            show_shaded="FALSE"
            shadetop="25"
            shadedown="-20"
            roundness="0.4"
            >
          </ThemeWidgetColors>
        </wcol_pulldown>
        <wcol_menu_back>
          <ThemeWidgetColors
            outline="#a6a6a6ff"
            inner="#c0c0c0ff"
            inner_sel="#cdcdcdff"
            item="#727272ff"
            text="#4d4d4d"
            text_sel="#1a1a1a"
            show_shaded="FALSE"
            shadetop="25"
            shadedown="-20"
            roundness="0.4"
            >
          </ThemeWidgetColors>
        </wcol_menu_back>
        <wcol_pie_menu>
          <ThemeWidgetColors
            outline="#333333ff"
            inner="#212121ef"
            inner_sel="#5680c2e6"
            item="#585858ff"
            text="#d9d9d9"
            text_sel="#ffffff"
            show_shaded="FALSE"
            shadetop="10"
            shadedown="-10"
            roundness="0.4"
            >
          </ThemeWidgetColors>
        </wcol_pie_menu>
        <wcol_tooltip>
          <ThemeWidgetColors
            outline="#19191aff"
            inner="#19191aef"
            inner_sel="#19191aef"
            item="#19191aef"
            text="#e6e6e6"
            text_sel="#ffffff"
            show_shaded="FALSE"
            shadetop="25"
            shadedown="-20"
            roundness="0.4"
            >
          </ThemeWidgetColors>
        </wcol_tooltip>
        <wcol_menu_item>
          <ThemeWidgetColors
            outline="#00000000"
            inner="#00000000"
            inner_sel="#5680c2e6"
            item="#ffffff8f"
            text="#1a1a1a"
            text_sel="#ffffff"
            show_shaded="FALSE"
            shadetop="38"
            shadedown="0"
            roundness="0.4"
            >
          </ThemeWidgetColors>
        </wcol_menu_item>
        <wcol_scroll>
          <ThemeWidgetColors
            outline="#999999ff"
            inner="#50505000"
            inner_sel="#646464b3"
            item="#303030ff"
            text="#000000"
            text_sel="#ffffff"
            show_shaded="FALSE"
            shadetop="5"
            shadedown="-5"
            roundness="1"
            >
          </ThemeWidgetColors>
        </wcol_scroll>
        <wcol_progress>
          <ThemeWidgetColors
            outline="#b3b3b3ff"
            inner="#ccccccff"
            inner_sel="#646464b4"
            item="#5094ffff"
            text="#000000"
            text_sel="#ffffff"
            show_shaded="FALSE"
            shadetop="0"
            shadedown="0"
            roundness="0.4"
            >
          </ThemeWidgetColors>
        </wcol_progress>
        <wcol_list_item>
          <ThemeWidgetColors
            outline="#e6e6e6ff"
            inner="#1a1a1a00"
            inner_sel="#668cccff"
            item="#ffffff33"
            text="#1a1a1a"
            text_sel="#ffffff"
            show_shaded="FALSE"
            shadetop="0"
            shadedown="0"
            roundness="0.4"
            >
          </ThemeWidgetColors>
        </wcol_list_item>
        <wcol_state>
          <ThemeWidgetStateColors
            error="#771111ff"
            warning="#ac8737ff"
            info="#28487dff"
            success="#188625ff"
            inner_anim="#73be4c"
            inner_anim_sel="#5aa633"
            inner_key="#f0eb64"
            inner_key_sel="#d7d34b"
            inner_driven="#b400ff"
            inner_driven_sel="#9900e6"
            inner_overridden="#6bf3cc"
            inner_overridden_sel="#5fd9b6"
            inner_changed="#cc7529"
            inner_changed_sel="#e6852d"
            blend="0.5"
            >
          </ThemeWidgetStateColors>
        </wcol_state>
        <wcol_tab>
          <ThemeWidgetColors
            outline="#33333300"
            inner="#808080cc"
            inner_sel="#b3b3b3ff"
            item="#28292dff"
            text="#1a1a1a"
            text_sel="#000000"
            show_shaded="FALSE"
            shadetop="0"
            shadedown="0"
            roundness="0.4"
            >
          </ThemeWidgetColors>
        </wcol_tab>
      </ThemeUserInterface>
    </user_interface>
    <view_3d>
      <ThemeView3D
        grid="#00000033"
        clipping_border_3d="#606060ff"
        wire="#000000"
        wire_edit="#000000"
        edge_width="1"
        gp_vertex="#000000"
        gp_vertex_select="#ff8500"
        gp_vertex_size="3"
        text_grease_pencil="#b5e61d"
        object_selected="#f15800"
        object_active="#ffaa40"
        text_keyframe="#ddd700"
        camera="#000000"
        empty="#000000"
        light="#00000028"
        speaker="#000000"
        vertex="#000000"
        vertex_select="#ff7a00"
        vertex_size="3"
        vertex_bevel="#00a5ff"
        vertex_unreferenced="#000000"
        edge_select="#ff9900"
        edge_mode_select="#ffd800"
        edge_seam="#db2512"
        edge_sharp="#00ffff"
        edge_crease="#cc0099"
        edge_bevel="#00a5ff"
        edge_facesel="#4b4b4b"
        freestyle_edge_mark="#7fff7f"
        face="#00000012"
        face_select="#ffa30033"
        face_mode_select="#ffb70033"
        face_dot="#ff8500"
        facedot_size="4"
        freestyle_face_mark="#7fff7f33"
        face_retopology="#50c8ff1e"
        face_back="#ff0000b3"
        face_front="#0000ff00"
        nurb_uline="#909000"
        nurb_vline="#803060"
        nurb_sel_uline="#f0ff40"
        nurb_sel_vline="#f090a0"
        act_spline="#db2512"
        handle_free="#000000"
        handle_auto="#909000"
        handle_vect="#409030"
        handle_sel_vect="#40c030"
        handle_align="#803060"
        handle_sel_free="#000000"
        handle_sel_auto="#f0ff40"
        handle_sel_align="#f090a0"
        lastsel_point="#ffffff"
        extra_edge_len="#ff6149"
        extra_edge_angle="#cccc00"
        extra_face_angle="#b3b3ff"
        extra_face_area="#00cc00"
        editmesh_active="#ffffff80"
        normal="#22dddd"
        vertex_normal="#2361dd"
        split_normal="#dd23dd"
        bone_pose="#50c8ff"
        bone_pose_active="#8cffff"
        bone_solid="#e6e6e6"
        bone_locked_weight="#ff000080"
        frame_current="#60c040"
        before_current_frame="#f22e23"
        after_current_frame="#78f244"
        bundle_solid="#c8c8c8"
        camera_path="#000000"
        camera_passepartout="#000000"
        skin_root="#b44d4d"
        view_overlay="#000000"
        transform="#ffffff"
        paint_curve_handle="#7fff7f7f"
        paint_curve_pivot="#ff7f7f7f"
        outline_width="1"
        object_origin_size="6"
        >
        <space>
          <ThemeSpaceGradient
            title="#000000"
            text="#000000"
            text_hi="#ffffff"
            header="#99999900"
            header_text="#000000"
            header_text_hi="#ffffff"
            button="#99999900"
            button_title="#1a1a1a"
            button_text="#000000"
            button_text_hi="#000000"
            navigation_bar="#00000000"
            execution_buts="#00000000"
            tab_active="#b3b3b3"
            tab_inactive="#8e8e8e"
            tab_back="#656565ff"
            tab_outline="#4d4d4dff"
            >
            <gradients>
              <ThemeGradientColors
                background_type="SINGLE_COLOR"
                high_gradient="#646464"
                gradient="#646464"
                >
              </ThemeGradientColors>
            </gradients>
            <panelcolors>
              <ThemePanelColors
                header="#ccccccff"
                back="#ccccccff"
                sub_back="#0000000f"
                >
              </ThemePanelColors>
            </panelcolors>
          </ThemeSpaceGradient>
        </space>
        <asset_shelf>
          <ThemeAssetShelf
            header_back="#adadadff"
            back="#999999ff"
            >
          </ThemeAssetShelf>
        </asset_shelf>
      </ThemeView3D>
    </view_3d>
    <graph_editor>
      <ThemeGraphEditor
        grid="#5e5e5e"
        frame_current="#5680c2"
        time_scrub_background="#808080e6"
        time_marker_line="#00000060"
        time_marker_line_selected="#ffffff60"
        window_sliders="#969696"
        channels_region="#999999"
        dopesheet_channel="#2e6399"
        dopesheet_subchannel="#7aa4cc"
        channel_group="#278c0e"
        active_channels_group="#4db135"
        preview_range="#a14d0066"
        vertex="#000000"
        vertex_select="#ff8500"
        vertex_active="#ffffff"
        vertex_size="6"
        vertex_bevel="#000000"
        vertex_unreferenced="#000000"
        handle_free="#000000"
        handle_auto="#909000"
        handle_vect="#409030"
        handle_sel_vect="#40c030"
        handle_align="#803060"
        handle_sel_free="#000000"
        handle_sel_auto="#f0ff40"
        handle_sel_align="#f090a0"
        handle_auto_clamped="#994030"
        handle_sel_auto_clamped="#f0af90"
        lastsel_point="#ffffff"
        handle_vertex="#000000"
        handle_vertex_select="#ff8500"
        handle_vertex_size="5"
        >
        <space>
          <ThemeSpaceGeneric
            back="#6b6b6b"
            title="#000000"
            text="#272727"
            text_hi="#ffffff"
            header="#adadadff"
            header_text="#000000"
            header_text_hi="#ffffff"
            button="#999999e6"
            button_title="#1a1a1a"
            button_text="#000000"
            button_text_hi="#000000"
            navigation_bar="#00000000"
            execution_buts="#00000000"
            tab_active="#6697e6"
            tab_inactive="#cccccc"
            tab_back="#999999ff"
            tab_outline="#999999ff"
            >
            <panelcolors>
              <ThemePanelColors
                header="#ccccccff"
                back="#ccccccff"
                sub_back="#0000000f"
                >
              </ThemePanelColors>
            </panelcolors>
          </ThemeSpaceGeneric>
        </space>
        <space_list>
          <ThemeSpaceListGeneric
            list="#adadad"
            list_title="#000000"
            list_text="#050505"
            list_text_hi="#ffffff"
            >
          </ThemeSpaceListGeneric>
        </space_list>
      </ThemeGraphEditor>
    </graph_editor>
    <file_browser>
      <ThemeFileBrowser
        selected_file="#5680c2"
        row_alternate="#ffffff0f"
        >
        <space>
          <ThemeSpaceGeneric
            back="#999999"
            title="#000000"
            text="#000000"
            text_hi="#ffffff"
            header="#adadadff"
            header_text="#000000"
            header_text_hi="#ffffff"
            button="#b3b3b3ff"
            button_title="#1a1a1a"
            button_text="#000000"
            button_text_hi="#000000"
            navigation_bar="#00000000"
            execution_buts="#999999e6"
            tab_active="#6697e6"
            tab_inactive="#cccccc"
            tab_back="#999999ff"
            tab_outline="#999999ff"
            >
            <panelcolors>
              <ThemePanelColors
                header="#ccccccff"
                back="#ccccccff"
                sub_back="#0000000f"
                >
              </ThemePanelColors>
            </panelcolors>
          </ThemeSpaceGeneric>
        </space>
      </ThemeFileBrowser>
    </file_browser>
    <nla_editor>
      <ThemeNLAEditor
        grid="#5e5e5e"
        view_sliders="#969696"
        dopesheet_channel="#5a85b2"
        dopesheet_subchannel="#7d98b3"
        nla_track="#424242"
        active_action="#cc701a66"
        active_action_unset="#9987614d"
        preview_range="#a14d0066"
        strips="#0c0a0a"
        strips_selected="#ff8c00"
        transition_strips="#1c2630"
        transition_strips_selected="#2e75db"
        meta_strips="#332642"
        meta_strips_selected="#692196"
        sound_strips="#2b3d3d"
        sound_strips_selected="#1f7a7a"
        tweak="#4df31a"
        tweak_duplicate="#d90000"
        keyframe_border="#000000ff"
        keyframe_border_selected="#000000ff"
        frame_current="#5680c2"
        time_scrub_background="#808080e6"
        time_marker_line="#00000060"
        time_marker_line_selected="#ffffff60"
        >
        <space>
          <ThemeSpaceGeneric
            back="#6b6b6b"
            title="#000000"
            text="#272727"
            text_hi="#ffffff"
            header="#adadadff"
            header_text="#000000"
            header_text_hi="#ffffff"
            button="#7272727f"
            button_title="#000000"
            button_text="#000000"
            button_text_hi="#000000"
            navigation_bar="#00000000"
            execution_buts="#00000000"
            tab_active="#6697e6"
            tab_inactive="#535353"
            tab_back="#404040ff"
            tab_outline="#3c3c3cff"
            >
            <panelcolors>
              <ThemePanelColors
                header="#ccccccff"
                back="#ccccccff"
                sub_back="#0000000f"
                >
              </ThemePanelColors>
            </panelcolors>
          </ThemeSpaceGeneric>
        </space>
        <space_list>
          <ThemeSpaceListGeneric
            list="#adadad"
            list_title="#000000"
            list_text="#000000"
            list_text_hi="#ffffff"
            >
          </ThemeSpaceListGeneric>
        </space_list>
      </ThemeNLAEditor>
    </nla_editor>
    <dopesheet_editor>
      <ThemeDopeSheet
        grid="#4d4d4d"
        frame_current="#5680c2"
        time_scrub_background="#808080e6"
        time_marker_line="#00000060"
        time_marker_line_selected="#ffffff60"
        value_sliders="#000000"
        view_sliders="#969696"
        dopesheet_channel="#2e639924"
        dopesheet_subchannel="#7aa4cc24"
        channels="#707070ff"
        channels_selected="#60c04044"
        channel_group="#278c0e37"
        active_channels_group="#4eb33555"
        long_key="#1a151580"
        long_key_selected="#ff8c00cc"
        keyframe="#e8e8e8"
        keyframe_selected="#ffbe33"
        keyframe_extreme="#e8b3cc"
        keyframe_extreme_selected="#f28080"
        keyframe_breakdown="#b3dbe8"
        keyframe_breakdown_selected="#54bfed"
        keyframe_jitter="#94e575"
        keyframe_jitter_selected="#61c042"
        keyframe_movehold="#5c5656"
        keyframe_movehold_selected="#ffaf23"
        keyframe_generated="#a7a7a7"
        keyframe_generated_selected="#c4a05f"
        keyframe_border="#000000ff"
        keyframe_border_selected="#000000ff"
        keyframe_scale_factor="1"
        summary="#d3660066"
        preview_range="#a14d0066"
        interpolation_line="#94e575cc"
        simulated_frames="#721e65ff"
        >
        <space>
          <ThemeSpaceGeneric
            back="#6b6b6b"
            title="#000000"
            text="#272727"
            text_hi="#ffffff"
            header="#adadadff"
            header_text="#000000"
            header_text_hi="#ffffff"
            button="#7272727f"
            button_title="#000000"
            button_text="#000000"
            button_text_hi="#000000"
            navigation_bar="#00000000"
            execution_buts="#00000000"
            tab_active="#6697e6"
            tab_inactive="#535353"
            tab_back="#404040ff"
            tab_outline="#3c3c3cff"
            >
            <panelcolors>
              <ThemePanelColors
                header="#ccccccff"
                back="#ccccccff"
                sub_back="#0000000f"
                >
              </ThemePanelColors>
            </panelcolors>
          </ThemeSpaceGeneric>
        </space>
        <space_list>
          <ThemeSpaceListGeneric
            list="#adadad"
            list_title="#000000"
            list_text="#000000"
            list_text_hi="#ffffff"
            >
          </ThemeSpaceListGeneric>
        </space_list>
      </ThemeDopeSheet>
    </dopesheet_editor>
    <image_editor>
      <ThemeImageEditor
        grid="#353535ff"
        vertex="#000000"
        vertex_select="#ff8500"
        vertex_size="3"
        vertex_bevel="#000000"
        vertex_unreferenced="#000000"
        face="#ffffff0a"
        face_select="#ff85003c"
        face_mode_select="#00000000"
        face_dot="#ff8500"
        facedot_size="3"
        freestyle_face_mark="#7fff7f33"
        face_retopology="#50c8ff1e"
        face_back="#ff0000b3"
        face_front="#0000ffb3"
        editmesh_active="#ffffff40"
        wire_edit="#c0c0c0"
        edge_width="1"
        edge_select="#ff8500"
        scope_back="#727272ff"
        preview_stitch_face="#7f7f0033"
        preview_stitch_edge="#ff00ff33"
        preview_stitch_vert="#0000ff33"
        preview_stitch_stitchable="#00ff00ff"
        preview_stitch_unstitchable="#ff0000ff"
        preview_stitch_active="#e1d2c323"
        uv_shadow="#707070ff"
        frame_current="#5680c2"
        metadatabg="#000000"
        metadatatext="#ffffff"
        handle_free="#000000"
        handle_auto="#909000"
        handle_align="#803060"
        handle_sel_free="#000000"
        handle_sel_auto="#f0ff40"
        handle_sel_align="#f090a0"
        handle_auto_clamped="#000000"
        handle_sel_auto_clamped="#000000"
        handle_vertex="#000000"
        handle_vertex_select="#ffff00"
        handle_vertex_size="5"
        paint_curve_handle="#7fff7f7f"
        paint_curve_pivot="#ff7f7f7f"
        >
        <space>
          <ThemeSpaceGeneric
            back="#353535"
            title="#000000"
            text="#000000"
            text_hi="#ffffff"
            header="#b3b3b3ff"
            header_text="#000000"
            header_text_hi="#ffffff"
            button="#99999900"
            button_title="#1a1a1a"
            button_text="#000000"
            button_text_hi="#000000"
            navigation_bar="#00000000"
            execution_buts="#00000000"
            tab_active="#b3b3b3"
            tab_inactive="#8e8e8e"
            tab_back="#656565ff"
            tab_outline="#4d4d4dff"
            >
            <panelcolors>
              <ThemePanelColors
                header="#ccccccff"
                back="#ccccccff"
                sub_back="#0000000f"
                >
              </ThemePanelColors>
            </panelcolors>
          </ThemeSpaceGeneric>
        </space>
        <asset_shelf>
          <ThemeAssetShelf
            header_back="#1d1d1dff"
            back="#303030ff"
            >
          </ThemeAssetShelf>
        </asset_shelf>
      </ThemeImageEditor>
    </image_editor>
    <sequence_editor>
      <ThemeSequenceEditor
        grid="#404040"
        window_sliders="#a0a0a0"
        movie_strip="#4d6890"
        movieclip_strip="#8f4c4c"
        image_strip="#8f744b"
        scene_strip="#828f50"
        audio_strip="#4c8f8f"
        effect_strip="#4c456c"
        transition_strip="#50458f"
        color_strip="#8f8f8f"
        meta_strip="#5b4d91"
        mask_strip="#8f5656"
        text_strip="#824c8f"
        active_strip="#ffffff"
        selected_strip="#ff6a00"
        frame_current="#5680c2"
        time_scrub_background="#292929e6"
        time_marker_line="#00000060"
        time_marker_line_selected="#ffffff60"
        keyframe="#ff8500"
        keyframe_selected="#ffbe33"
        keyframe_breakdown="#b3dbe8"
        keyframe_breakdown_selected="#54bfed"
        keyframe_movehold="#808080"
        keyframe_movehold_selected="#ffaf23"
        keyframe_generated="#585858"
        keyframe_generated_selected="#a28962"
        keyframe_border="#000000ff"
        keyframe_border_selected="#000000ff"
        draw_action="#50c8ff"
        preview_back="#000000"
        metadatabg="#000000"
        metadatatext="#ffffff"
        preview_range="#a14d0066"
        row_alternate="#ffffff0d"
        text_strip_cursor="#71a8ffff"
        selected_text="#19191a4d"
        >
        <space>
          <ThemeSpaceGeneric
            back="#353535"
            title="#000000"
            text="#747474"
            text_hi="#ffffff"
            header="#b3b3b3ff"
            header_text="#000000"
            header_text_hi="#ffffff"
            button="#99999900"
            button_title="#1a1a1a"
            button_text="#000000"
            button_text_hi="#000000"
            navigation_bar="#00000000"
            execution_buts="#00000000"
            tab_active="#b3b3b3"
            tab_inactive="#8e8e8e"
            tab_back="#656565ff"
            tab_outline="#4d4d4dff"
            >
            <panelcolors>
              <ThemePanelColors
                header="#ccccccff"
                back="#ccccccff"
                sub_back="#0000000f"
                >
              </ThemePanelColors>
            </panelcolors>
          </ThemeSpaceGeneric>
        </space>
        <space_list>
          <ThemeSpaceListGeneric
            list="#b3b3b3"
            list_title="#747474"
            list_text="#333333"
            list_text_hi="#747474"
            >
          </ThemeSpaceListGeneric>
        </space_list>
      </ThemeSequenceEditor>
    </sequence_editor>
    <properties>
      <ThemeProperties
        match="#5680c2"
        active_modifier="#5680c2ff"
        >
        <space>
          <ThemeSpaceGeneric
            back="#b3b3b3"
            title="#181818"
            text="#000000"
            text_hi="#ffffff"
            header="#b3b3b3ff"
            header_text="#000000"
            header_text_hi="#ffffff"
            button="#7272727f"
            button_title="#000000"
            button_text="#000000"
            button_text_hi="#000000"
            navigation_bar="#a6a6a6ff"
            execution_buts="#00000000"
            tab_active="#6697e6"
            tab_inactive="#535353"
            tab_back="#404040ff"
            tab_outline="#3c3c3cff"
            >
            <panelcolors>
              <ThemePanelColors
                header="#ccccccff"
                back="#ccccccff"
                sub_back="#0000000f"
                >
              </ThemePanelColors>
            </panelcolors>
          </ThemeSpaceGeneric>
        </space>
      </ThemeProperties>
    </properties>
    <text_editor>
      <ThemeTextEditor
        line_numbers="#d0d0d0"
        line_numbers_background="#313133"
        selected_text="#19191a"
        cursor="#71a8ff"
        syntax_builtin="#ff1961"
        syntax_symbols="#ff734d"
        syntax_special="#95d600"
        syntax_preprocessor="#ad80ff"
        syntax_reserved="#c4753b"
        syntax_comment="#939393"
        syntax_string="#f6e162"
        syntax_numbers="#50faff"
        >
        <space>
          <ThemeSpaceGeneric
            back="#313133"
            title="#000000"
            text="#e6e6e6"
            text_hi="#ffffff"
            header="#b3b3b3ff"
            header_text="#000000"
            header_text_hi="#ffffff"
            button="#7272727f"
            button_title="#000000"
            button_text="#000000"
            button_text_hi="#000000"
            navigation_bar="#00000000"
            execution_buts="#00000000"
            tab_active="#6697e6"
            tab_inactive="#535353"
            tab_back="#404040ff"
            tab_outline="#3c3c3cff"
            >
            <panelcolors>
              <ThemePanelColors
                header="#ccccccff"
                back="#ccccccff"
                sub_back="#0000000f"
                >
              </ThemePanelColors>
            </panelcolors>
          </ThemeSpaceGeneric>
        </space>
      </ThemeTextEditor>
    </text_editor>
    <node_editor>
      <ThemeNodeEditor
        grid="#282828"
        node_selected="#ed5700"
        node_active="#ffffff"
        wire="#1a1a1aff"
        wire_inner="#8d8d8d"
        wire_select="#ffffffb3"
        selected_text="#7f7f7f"
        node_backdrop="#666666ff"
        converter_node="#12adff"
        color_node="#cccc00"
        group_node="#3b660a"
        group_socket_node="#000000"
        frame_node="#0f0f0fcc"
        matte_node="#973c3c"
        distor_node="#4c9797"
        noodle_curving="4"
        grid_levels="3"
        dash_alpha="0.5"
        input_node="#ff3371"
        output_node="#4d0017"
        filter_node="#551a80"
        vector_node="#4d4dff"
        texture_node="#e66800"
        shader_node="#24b524"
        script_node="#084d4d"
        pattern_node="#6c696f"
        layout_node="#6c696f"
        geometry_node="#00d6a3"
        attribute_node="#001566"
        simulation_zone="#66416233"
        repeat_zone="#76512f33"
        foreach_geometry_element_zone="#33527f33"
        closure_zone="#527f3333"
        >
        <space>
          <ThemeSpaceGeneric
            back="#1d1d1d"
            title="#eeeeee"
            text="#e6e6e6"
            text_hi="#ffffff"
            header="#b3b3b3ff"
            header_text="#eeeeee"
            header_text_hi="#ffffff"
            button="#99999900"
            button_title="#1a1a1a"
            button_text="#000000"
            button_text_hi="#000000"
            navigation_bar="#00000000"
            execution_buts="#00000000"
            tab_active="#b3b3b3"
            tab_inactive="#8e8e8e"
            tab_back="#656565ff"
            tab_outline="#4d4d4dff"
            >
            <panelcolors>
              <ThemePanelColors
                header="#ccccccff"
                back="#ccccccff"
                sub_back="#0000000f"
                >
              </ThemePanelColors>
            </panelcolors>
          </ThemeSpaceGeneric>
        </space>
        <space_list>
          <ThemeSpaceListGeneric
            list="#a5a5a5"
            list_title="#000000"
            list_text="#000000"
            list_text_hi="#ffffff"
            >
          </ThemeSpaceListGeneric>
        </space_list>
      </ThemeNodeEditor>
    </node_editor>
    <outliner>
      <ThemeOutliner
        match="#337f33"
        selected_highlight="#7a8499"
        active="#929eb7"
        selected_object="#ffddb3"
        active_object="#ffffff"
        edited_object="#0080624d"
        row_alternate="#ffffff0f"
        >
        <space>
          <ThemeSpaceGeneric
            back="#999999"
            title="#000000"
            text="#000000"
            text_hi="#ffffff"
            header="#b3b3b3ff"
            header_text="#000000"
            header_text_hi="#ffffff"
            button="#7272727f"
            button_title="#000000"
            button_text="#000000"
            button_text_hi="#000000"
            navigation_bar="#00000000"
            execution_buts="#00000000"
            tab_active="#6697e6"
            tab_inactive="#535353"
            tab_back="#404040ff"
            tab_outline="#3c3c3cff"
            >
            <panelcolors>
              <ThemePanelColors
                header="#ccccccff"
                back="#ccccccff"
                sub_back="#0000000f"
                >
              </ThemePanelColors>
            </panelcolors>
          </ThemeSpaceGeneric>
        </space>
      </ThemeOutliner>
    </outliner>
    <info>
      <ThemeInfo
        info_selected="#6080ff"
        info_selected_text="#000000"
        info_error_text="#000000"
        info_warning_text="#000000"
        info_info_text="#000000"
        info_debug="#b30095ff"
        info_debug_text="#000000"
        info_property="#44b300ff"
        info_property_text="#000000"
        info_operator="#44b300ff"
        info_operator_text="#000000"
        >
        <space>
          <ThemeSpaceGeneric
            back="#727272"
            title="#000000"
            text="#000000"
            text_hi="#ffffff"
            header="#b3b3b3ff"
            header_text="#1a1a1a"
            header_text_hi="#ffffff"
            button="#7272727f"
            button_title="#000000"
            button_text="#000000"
            button_text_hi="#000000"
            navigation_bar="#00000000"
            execution_buts="#00000000"
            tab_active="#6697e6"
            tab_inactive="#535353"
            tab_back="#404040ff"
            tab_outline="#3c3c3cff"
            >
            <panelcolors>
              <ThemePanelColors
                header="#ccccccff"
                back="#ccccccff"
                sub_back="#0000000f"
                >
              </ThemePanelColors>
            </panelcolors>
          </ThemeSpaceGeneric>
        </space>
      </ThemeInfo>
    </info>
    <preferences>
      <ThemePreferences>
        <space>
          <ThemeSpaceGeneric
            back="#b3b3b3"
            title="#181818"
            text="#000000"
            text_hi="#ffffff"
            header="#b3b3b3ff"
            header_text="#000000"
            header_text_hi="#ffffff"
            button="#7272727f"
            button_title="#000000"
            button_text="#000000"
            button_text_hi="#000000"
            navigation_bar="#b3b3b3ff"
            execution_buts="#b3b3b3ff"
            tab_active="#6697e6"
            tab_inactive="#535353"
            tab_back="#404040ff"
            tab_outline="#3c3c3cff"
            >
            <panelcolors>
              <ThemePanelColors
                header="#ccccccff"
                back="#ccccccff"
                sub_back="#0000000f"
                >
              </ThemePanelColors>
            </panelcolors>
          </ThemeSpaceGeneric>
        </space>
      </ThemePreferences>
    </preferences>
    <console>
      <ThemeConsole
        line_output="#71a8ff"
        line_input="#f2f2f2"
        line_info="#95d600"
        line_error="#ff4d84"
        cursor="#71a8ff"
        select="#ffffff30"
        >
        <space>
          <ThemeSpaceGeneric
            back="#1d1d1d"
            title="#eeeeee"
            text="#e6e6e6"
            text_hi="#ffffff"
            header="#b3b3b3ff"
            header_text="#000000"
            header_text_hi="#ffffff"
            button="#30303000"
            button_title="#ffffff"
            button_text="#cccccc"
            button_text_hi="#ffffff"
            navigation_bar="#00000000"
            execution_buts="#00000000"
            tab_active="#6697e6"
            tab_inactive="#535353"
            tab_back="#404040ff"
            tab_outline="#3c3c3cff"
            >
            <panelcolors>
              <ThemePanelColors
                header="#ccccccff"
                back="#ccccccff"
                sub_back="#0000000f"
                >
              </ThemePanelColors>
            </panelcolors>
          </ThemeSpaceGeneric>
        </space>
      </ThemeConsole>
    </console>
    <clip_editor>
      <ThemeClipEditor
        grid="#393939ff"
        marker_outline="#000000"
        marker="#7f7f00"
        active_marker="#ffffff"
        selected_marker="#ffff00"
        disabled_marker="#7f0000"
        locked_marker="#7f7f7f"
        path_before="#ff0000"
        path_after="#0000ff"
        path_keyframe_before="#ffc4c4"
        path_keyframe_after="#c4c4ff"
        frame_current="#5680c2"
        preview_range="#a14d0066"
        time_scrub_background="#292929e6"
        time_marker_line="#00000060"
        time_marker_line_selected="#ffffff60"
        strips="#0c0a0a"
        strips_selected="#ff8c00"
        metadatabg="#000000"
        metadatatext="#ffffff"
        handle_free="#000000"
        handle_auto="#909000"
        handle_align="#803060"
        handle_sel_free="#000000"
        handle_sel_auto="#f0ff40"
        handle_sel_align="#f090a0"
        handle_auto_clamped="#000000"
        handle_sel_auto_clamped="#000000"
        handle_vertex="#000000"
        handle_vertex_select="#ffff00"
        handle_vertex_size="5"
        >
        <space>
          <ThemeSpaceGeneric
            back="#393939"
            title="#000000"
            text="#000000"
            text_hi="#ffffff"
            header="#b3b3b3ff"
            header_text="#000000"
            header_text_hi="#ffffff"
            button="#7272727f"
            button_title="#000000"
            button_text="#000000"
            button_text_hi="#000000"
            navigation_bar="#00000000"
            execution_buts="#00000000"
            tab_active="#6697e6"
            tab_inactive="#535353"
            tab_back="#404040ff"
            tab_outline="#3c3c3cff"
            >
            <panelcolors>
              <ThemePanelColors
                header="#ccccccff"
                back="#ccccccff"
                sub_back="#0000000f"
                >
              </ThemePanelColors>
            </panelcolors>
          </ThemeSpaceGeneric>
        </space>
        <space_list>
          <ThemeSpaceListGeneric
            list="#666666"
            list_title="#000000"
            list_text="#000000"
            list_text_hi="#ffffff"
            >
          </ThemeSpaceListGeneric>
        </space_list>
      </ThemeClipEditor>
    </clip_editor>
    <topbar>
      <ThemeTopBar>
        <space>
          <ThemeSpaceGeneric
            back="#b3b3b3"
            title="#ffffff"
            text="#ffffff"
            text_hi="#ffffff"
            header="#999999ff"
            header_text="#eeeeee"
            header_text_hi="#ffffff"
            button="#2f303599"
            button_title="#ffffff"
            button_text="#000000"
            button_text_hi="#000000"
            navigation_bar="#00000000"
            execution_buts="#00000000"
            tab_active="#446499"
            tab_inactive="#28292d"
            tab_back="#28292dff"
            tab_outline="#28292dff"
            >
            <panelcolors>
              <ThemePanelColors
                header="#ccccccff"
                back="#ccccccff"
                sub_back="#0000000f"
                >
              </ThemePanelColors>
            </panelcolors>
          </ThemeSpaceGeneric>
        </space>
      </ThemeTopBar>
    </topbar>
    <statusbar>
      <ThemeStatusBar>
        <space>
          <ThemeSpaceGeneric
            back="#393939"
            title="#ffffff"
            text="#ffffff"
            text_hi="#ffffff"
            header="#999999ff"
            header_text="#1a1a1a"
            header_text_hi="#ffffff"
            button="#2f303500"
            button_title="#ffffff"
            button_text="#ffffff"
            button_text_hi="#000000"
            navigation_bar="#00000000"
            execution_buts="#00000000"
            tab_active="#446499"
            tab_inactive="#28292d"
            tab_back="#28292dff"
            tab_outline="#28292dff"
            >
            <panelcolors>
              <ThemePanelColors
                header="#ccccccff"
                back="#ccccccff"
                sub_back="#0000000f"
                >
              </ThemePanelColors>
            </panelcolors>
          </ThemeSpaceGeneric>
        </space>
      </ThemeStatusBar>
    </statusbar>
    <spreadsheet>
      <ThemeSpreadsheet
        row_alternate="#ffffff0f"
        >
        <space>
          <ThemeSpaceGeneric
            back="#999999"
            title="#000000"
            text="#000000"
            text_hi="#ffffff"
            header="#adadadff"
            header_text="#000000"
            header_text_hi="#ffffff"
            button="#b3b3b3ff"
            button_title="#1a1a1a"
            button_text="#000000"
            button_text_hi="#000000"
            navigation_bar="#00000000"
            execution_buts="#999999e6"
            tab_active="#6697e6"
            tab_inactive="#cccccc"
            tab_back="#999999ff"
            tab_outline="#999999ff"
            >
            <panelcolors>
              <ThemePanelColors
                header="#ccccccff"
                back="#ccccccff"
                sub_back="#0000000f"
                >
              </ThemePanelColors>
            </panelcolors>
          </ThemeSpaceGeneric>
        </space>
        <space_list>
          <ThemeSpaceListGeneric
            list="#adadad"
            list_title="#c3c3c3"
            list_text="#c3c3c3"
            list_text_hi="#00ffff"
            >
          </ThemeSpaceListGeneric>
        </space_list>
      </ThemeSpreadsheet>
    </spreadsheet>
    <bone_color_sets>
      <ThemeBoneColorSet
        normal="#9a0000"
        select="#bd1111"
        active="#f70a0a"
        show_colored_constraints="FALSE"
        >
      </ThemeBoneColorSet>
      <ThemeBoneColorSet
        normal="#f74018"
        select="#f66913"
        active="#fa9900"
        show_colored_constraints="FALSE"
        >
      </ThemeBoneColorSet>
      <ThemeBoneColorSet
        normal="#1e9109"
        select="#59b70b"
        active="#83ef1d"
        show_colored_constraints="FALSE"
        >
      </ThemeBoneColorSet>
      <ThemeBoneColorSet
        normal="#0a3694"
        select="#3667df"
        active="#5ec1ef"
        show_colored_constraints="FALSE"
        >
      </ThemeBoneColorSet>
      <ThemeBoneColorSet
        normal="#a9294e"
        select="#c1416a"
        active="#f05d91"
        show_colored_constraints="FALSE"
        >
      </ThemeBoneColorSet>
      <ThemeBoneColorSet
        normal="#430c78"
        select="#543aa3"
        active="#8764d5"
        show_colored_constraints="FALSE"
        >
      </ThemeBoneColorSet>
      <ThemeBoneColorSet
        normal="#24785a"
        select="#3c9579"
        active="#6fb6ab"
        show_colored_constraints="FALSE"
        >
      </ThemeBoneColorSet>
      <ThemeBoneColorSet
        normal="#4b707c"
        select="#6a8691"
        active="#9bc2cd"
        show_colored_constraints="FALSE"
        >
      </ThemeBoneColorSet>
      <ThemeBoneColorSet
        normal="#f4c90c"
        select="#eec236"
        active="#f3ff00"
        show_colored_constraints="FALSE"
        >
      </ThemeBoneColorSet>
      <ThemeBoneColorSet
        normal="#1e2024"
        select="#484c56"
        active="#ffffff"
        show_colored_constraints="FALSE"
        >
      </ThemeBoneColorSet>
      <ThemeBoneColorSet
        normal="#6f2f6a"
        select="#9845be"
        active="#d330d6"
        show_colored_constraints="FALSE"
        >
      </ThemeBoneColorSet>
      <ThemeBoneColorSet
        normal="#6c8e22"
        select="#7fb022"
        active="#bbef5b"
        show_colored_constraints="FALSE"
        >
      </ThemeBoneColorSet>
      <ThemeBoneColorSet
        normal="#8d8d8d"
        select="#b0b0b0"
        active="#dedede"
        show_colored_constraints="FALSE"
        >
      </ThemeBoneColorSet>
      <ThemeBoneColorSet
        normal="#834326"
        select="#8b5811"
        active="#bd6a11"
        show_colored_constraints="FALSE"
        >
      </ThemeBoneColorSet>
      <ThemeBoneColorSet
        normal="#08310e"
        select="#1c430b"
        active="#34622b"
        show_colored_constraints="FALSE"
        >
      </ThemeBoneColorSet>
      <ThemeBoneColorSet
        normal="#000000"
        select="#000000"
        active="#000000"
        show_colored_constraints="FALSE"
        >
      </ThemeBoneColorSet>
      <ThemeBoneColorSet
        normal="#000000"
        select="#000000"
        active="#000000"
        show_colored_constraints="FALSE"
        >
      </ThemeBoneColorSet>
      <ThemeBoneColorSet
        normal="#000000"
        select="#000000"
        active="#000000"
        show_colored_constraints="FALSE"
        >
      </ThemeBoneColorSet>
      <ThemeBoneColorSet
        normal="#000000"
        select="#000000"
        active="#000000"
        show_colored_constraints="FALSE"
        >
      </ThemeBoneColorSet>
      <ThemeBoneColorSet
        normal="#000000"
        select="#000000"
        active="#000000"
        show_colored_constraints="FALSE"
        >
      </ThemeBoneColorSet>
    </bone_color_sets>
    <collection_color>
      <ThemeCollectionColor
        color="#dd473f"
        >
      </ThemeCollectionColor>
      <ThemeCollectionColor
        color="#e5a057"
        >
      </ThemeCollectionColor>
      <ThemeCollectionColor
        color="#e4d050"
        >
      </ThemeCollectionColor>
      <ThemeCollectionColor
        color="#3fb931"
        >
      </ThemeCollectionColor>
      <ThemeCollectionColor
        color="#3887c7"
        >
      </ThemeCollectionColor>
      <ThemeCollectionColor
        color="#844fcd"
        >
      </ThemeCollectionColor>
      <ThemeCollectionColor
        color="#c169b5"
        >
      </ThemeCollectionColor>
      <ThemeCollectionColor
        color="#7a5441"
        >
      </ThemeCollectionColor>
    </collection_color>
    <strip_color>
      <ThemeStripColor
        color="#e2605b"
        >
      </ThemeStripColor>
      <ThemeStripColor
        color="#f1a355"
        >
      </ThemeStripColor>
      <ThemeStripColor
        color="#f1dc55"
        >
      </ThemeStripColor>
      <ThemeStripColor
        color="#7bcc7b"
        >
      </ThemeStripColor>
      <ThemeStripColor
        color="#5db6ea"
        >
      </ThemeStripColor>
      <ThemeStripColor
        color="#8d59da"
        >
      </ThemeStripColor>
      <ThemeStripColor
        color="#c673b8"
        >
      </ThemeStripColor>
      <ThemeStripColor
        color="#7a5441"
        >
      </ThemeStripColor>
      <ThemeStripColor
        color="#5f5f5f"
        >
      </ThemeStripColor>
    </strip_color>
  </Theme>
  <ThemeStyle>
    <panel_title>
      <ThemeFontStyle
        points="12"
        character_weight="400"
        shadow="3"
        shadow_offset_x="0"
        shadow_offset_y="-1"
        shadow_alpha="1"
        shadow_value="0.8"
        >
      </ThemeFontStyle>
    </panel_title>
    <widget>
      <ThemeFontStyle
        points="11"
        character_weight="400"
        shadow="1"
        shadow_offset_x="0"
        shadow_offset_y="-1"
        shadow_alpha="0"
        shadow_value="0.8"
        >
      </ThemeFontStyle>
    </widget>
    <tooltip>
      <ThemeFontStyle
        points="11"
        character_weight="400"
        shadow="1"
        shadow_offset_x="0"
        shadow_offset_y="-1"
        shadow_alpha="0.5"
        shadow_value="0"
        >
      </ThemeFontStyle>
    </tooltip>
  </ThemeStyle>
</bpy>
