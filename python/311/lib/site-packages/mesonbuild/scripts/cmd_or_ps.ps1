# Copied from GStreamer project
# Author: <PERSON><PERSON><PERSON> <<EMAIL>>

$i=1
$ppid=(gwmi win32_process -Filter "processid='$pid'").parentprocessid
$pname=(Get-Process -id $ppid).Name
While($true) {
  if($pname -eq "cmd" -Or $pname -eq "powershell" -Or $pname -eq "pwsh") {
    Write-Host ("{0}.exe" -f $pname)
    Break
  }

  # 10 times iteration seems to be sufficient
  if($i -gt 10) {
    Break
  }

  # not found yet, find grand parant
  $ppid=(gwmi win32_process -Filter "processid='$ppid'").parentprocessid
  $pname=(Get-Process -id $ppid).Name
  $i++
}
