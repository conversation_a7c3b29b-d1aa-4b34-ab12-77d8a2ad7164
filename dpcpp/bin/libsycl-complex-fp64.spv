#     �                       
          &     '        OpenCL.std               爢      __devicelib_cimag        __devicelib_creal     
   __devicelib_cabs         __devicelib_carg         __devicelib_cproj        __devicelib_cexp         __devicelib_clog         __devicelib_cpow          __devicelib_cpolar    $   __devicelib_csqrt     '   __devicelib_csinh     *   __devicelib_ccosh     -   __devicelib_ctanh     0   __devicelib_csin      3   __devicelib_ccos      6   __devicelib_ctan      9   __devicelib_cacos     <   __devicelib_casinh    ?   __devicelib_casin     B   __devicelib_cacosh    E   __devicelib_catanh    H   __devicelib_catan     L   __devicelib___muldc3      R   __devicelib___divdc3      X   cimag     Y   z     Z   entry     [   byval-temp    ^   z.ascast.real     d   z.ascast.imagp    f   z.ascast.imag     i   byval-temp.imagp      l   call      n   creal     o   z     p   entry     q   byval-temp    s   z.ascast.real     u   z.ascast.imagp    w   z.ascast.imag     z   byval-temp.imagp      }   call         cabs      �   z     �   entry     �   byval-temp    �   z.ascast.real     �   z.ascast.imagp    �   z.ascast.imag     �   byval-temp.imagp      �   call      �   carg      �   z     �   entry     �   byval-temp    �   z.ascast.real     �   z.ascast.imagp    �   z.ascast.imag     �   byval-temp.imagp      �   call      �   cproj     �   agg.result    �   z     �   entry     �   tmp   �   byval-temp    �   tmp.ascast    �   z.ascast.real     �   z.ascast.imagp    �   z.ascast.imag     �   byval-temp.imagp      �   tmp.ascast.real   �   tmp.ascast.imagp      �   tmp.ascast.imag   �   agg.result.imagp      �   cexp      �   agg.result    �   z     �   entry     �   tmp   �   byval-temp    �   tmp.ascast    �   z.ascast.real     �   z.ascast.imagp    �   z.ascast.imag     �   byval-temp.imagp      �   tmp.ascast.real   �   tmp.ascast.imagp      �   tmp.ascast.imag   �   agg.result.imagp      �   clog      �   agg.result    �   z     �   entry     �   tmp   �   byval-temp    �   tmp.ascast    �   z.ascast.real     �   z.ascast.imagp    �   z.ascast.imag     �   byval-temp.imagp      �   tmp.ascast.real   �   tmp.ascast.imagp      �   tmp.ascast.imag   �   agg.result.imagp      �   cpow      �   agg.result    �   x        y       entry       tmp     byval-temp      byval-temp1     tmp.ascast      x.ascast.real     	  x.ascast.imagp      x.ascast.imag     
  y.ascast.real       y.ascast.imagp      y.ascast.imag       byval-temp.imagp        byval-temp1.imagp       tmp.ascast.real      tmp.ascast.imagp      "  tmp.ascast.imag   &  agg.result.imagp      )  cpolar    *  agg.result    +  rho   ,  theta     -  entry     .  tmp   /  tmp.ascast    2  tmp.ascast.real   4  tmp.ascast.imagp      6  tmp.ascast.imag   8  agg.result.imagp      ;  csqrt     <  agg.result    =  z     >  entry     ?  tmp   @  byval-temp    A  tmp.ascast    C  z.ascast.real     E  z.ascast.imagp    G  z.ascast.imag     J  byval-temp.imagp      O  tmp.ascast.real   Q  tmp.ascast.imagp      S  tmp.ascast.imag   V  agg.result.imagp      Y  csinh     Z  agg.result    [  z     \  entry     ]  tmp   ^  byval-temp    _  tmp.ascast    a  z.ascast.real     c  z.ascast.imagp    e  z.ascast.imag     h  byval-temp.imagp      m  tmp.ascast.real   o  tmp.ascast.imagp      q  tmp.ascast.imag   t  agg.result.imagp      w  ccosh     x  agg.result    y  z     z  entry     {  tmp   |  byval-temp    }  tmp.ascast      z.ascast.real     �  z.ascast.imagp    �  z.ascast.imag     �  byval-temp.imagp      �  tmp.ascast.real   �  tmp.ascast.imagp      �  tmp.ascast.imag   �  agg.result.imagp      �  ctanh     �  agg.result    �  z     �  entry     �  tmp   �  byval-temp    �  tmp.ascast    �  z.ascast.real     �  z.ascast.imagp    �  z.ascast.imag     �  byval-temp.imagp      �  tmp.ascast.real   �  tmp.ascast.imagp      �  tmp.ascast.imag   �  agg.result.imagp      �  csin      �  agg.result    �  z     �  entry     �  tmp   �  byval-temp    �  tmp.ascast    �  z.ascast.real     �  z.ascast.imagp    �  z.ascast.imag     �  byval-temp.imagp      �  tmp.ascast.real   �  tmp.ascast.imagp      �  tmp.ascast.imag   �  agg.result.imagp      �  ccos      �  agg.result    �  z     �  entry     �  tmp   �  byval-temp    �  tmp.ascast    �  z.ascast.real     �  z.ascast.imagp    �  z.ascast.imag     �  byval-temp.imagp      �  tmp.ascast.real   �  tmp.ascast.imagp      �  tmp.ascast.imag   �  agg.result.imagp      �  ctan      �  agg.result    �  z     �  entry     �  tmp   �  byval-temp    �  tmp.ascast    �  z.ascast.real     �  z.ascast.imagp    �  z.ascast.imag     �  byval-temp.imagp        tmp.ascast.real     tmp.ascast.imagp        tmp.ascast.imag   
  agg.result.imagp      
  cacos       agg.result      z       entry       tmp     byval-temp      tmp.ascast      z.ascast.real       z.ascast.imagp      z.ascast.imag       byval-temp.imagp      !  tmp.ascast.real   #  tmp.ascast.imagp      %  tmp.ascast.imag   (  agg.result.imagp      +  casinh    ,  agg.result    -  z     .  entry     /  tmp   0  byval-temp    1  tmp.ascast    3  z.ascast.real     5  z.ascast.imagp    7  z.ascast.imag     :  byval-temp.imagp      ?  tmp.ascast.real   A  tmp.ascast.imagp      C  tmp.ascast.imag   F  agg.result.imagp      I  casin     J  agg.result    K  z     L  entry     M  tmp   N  byval-temp    O  tmp.ascast    Q  z.ascast.real     S  z.ascast.imagp    U  z.ascast.imag     X  byval-temp.imagp      ]  tmp.ascast.real   _  tmp.ascast.imagp      a  tmp.ascast.imag   d  agg.result.imagp      g  cacosh    h  agg.result    i  z     j  entry     k  tmp   l  byval-temp    m  tmp.ascast    o  z.ascast.real     q  z.ascast.imagp    s  z.ascast.imag     v  byval-temp.imagp      {  tmp.ascast.real   }  tmp.ascast.imagp        tmp.ascast.imag   �  agg.result.imagp      �  catanh    �  agg.result    �  z     �  entry     �  tmp   �  byval-temp    �  tmp.ascast    �  z.ascast.real     �  z.ascast.imagp    �  z.ascast.imag     �  byval-temp.imagp      �  tmp.ascast.real   �  tmp.ascast.imagp      �  tmp.ascast.imag   �  agg.result.imagp      �  catan     �  agg.result    �  z     �  entry     �  tmp   �  byval-temp    �  tmp.ascast    �  z.ascast.real     �  z.ascast.imagp    �  z.ascast.imag     �  byval-temp.imagp      �  tmp.ascast.real   �  tmp.ascast.imagp      �  tmp.ascast.imag   �  agg.result.imagp      �  __muldc3      �  agg.result    �  __a   �  __b   �  __c   �  __d   �  entry     �  tmp   �  tmp.ascast    �  tmp.ascast.real   �  tmp.ascast.imagp      �  tmp.ascast.imag   �  agg.result.imagp      �  __divdc3      �  agg.result    �  __a   �  __b   �  __c   �  __d   �  entry     �  tmp   �  tmp.ascast    �  tmp.ascast.real   �  tmp.ascast.imagp      �  tmp.ascast.imag   �  agg.result.imagp    G 	    )   __devicelib_cimag      G     &      G     ,      G 	    )   __devicelib_creal      G  	   &      G  	   ,      G 	 
   )   __devicelib_cabs       G     &      G     ,      G 	    )   __devicelib_carg       G  
   &      G  
   ,      G 	    )   __devicelib_cproj      G     &      G     ,      G     &      G     ,      G 	    )   __devicelib_cexp       G     &      G     ,      G     &      G     ,      G 	    )   __devicelib_clog       G     &      G     ,      G     &      G     ,      G 	    )   __devicelib_cpow       G     &      G     ,      G     &      G     ,      G     &      G     ,      G 	     )   __devicelib_cpolar     G  !   &      G  !   ,      G 	 $   )   __devicelib_csqrt      G  %   &      G  %   ,      G  &   &      G  &   ,      G 	 '   )   __devicelib_csinh      G  (   &      G  (   ,      G  )   &      G  )   ,      G 	 *   )   __devicelib_ccosh      G  +   &      G  +   ,      G  ,   &      G  ,   ,      G 	 -   )   __devicelib_ctanh      G  .   &      G  .   ,      G  /   &      G  /   ,      G 	 0   )   __devicelib_csin       G  1   &      G  1   ,      G  2   &      G  2   ,      G 	 3   )   __devicelib_ccos       G  4   &      G  4   ,      G  5   &      G  5   ,      G 	 6   )   __devicelib_ctan       G  7   &      G  7   ,      G  8   &      G  8   ,      G 	 9   )   __devicelib_cacos      G  :   &      G  :   ,      G  ;   &      G  ;   ,      G 	 <   )   __devicelib_casinh     G  =   &      G  =   ,      G  >   &      G  >   ,      G 	 ?   )   __devicelib_casin      G  @   &      G  @   ,      G  A   &      G  A   ,      G 	 B   )   __devicelib_cacosh     G  C   &      G  C   ,      G  D   &      G  D   ,      G 	 E   )   __devicelib_catanh     G  F   &      G  F   ,      G  G   &      G  G   ,      G 	 H   )   __devicelib_catan      G  I   &      G  I   ,      G  J   &      G  J   ,      G 
 L   )   __devicelib___muldc3       G  M   &      G  M   ,      G 
 R   )   __devicelib___divdc3       G  S   &      G  S   ,      G  X   )   cimag       G  Y   &      G  Y   ,      G  [   ,      G  n   )   creal       G  o   &      G  o   ,      G  q   ,      G     )   cabs        G  �   &      G  �   ,      G  �   ,      G  �   )   carg        G  �   &      G  �   ,      G  �   ,      G  �   )   cproj       G  �   &      G  �   &      G  �   ,      G  �   &      G  �   ,      G  �   ,      G  �   ,      G  �   )   cexp        G  �   &      G  �   &      G  �   ,      G  �   &      G  �   ,      G  �   ,      G  �   ,      G  �   )   clog        G  �   &      G  �   &      G  �   ,      G  �   &      G  �   ,      G  �   ,      G  �   ,      G  �   )   cpow        G  �   &      G  �   &      G  �   ,      G  �   &      G  �   ,      G     &      G     ,      G    ,      G    ,      G    ,      G  )  )   cpolar      G  *  &      G  *  &      G  *  ,      G  .  ,      G  ;  )   csqrt       G  <  &      G  <  &      G  <  ,      G  =  &      G  =  ,      G  ?  ,      G  @  ,      G  Y  )   csinh       G  Z  &      G  Z  &      G  Z  ,      G  [  &      G  [  ,      G  ]  ,      G  ^  ,      G  w  )   ccosh       G  x  &      G  x  &      G  x  ,      G  y  &      G  y  ,      G  {  ,      G  |  ,      G  �  )   ctanh       G  �  &      G  �  &      G  �  ,      G  �  &      G  �  ,      G  �  ,      G  �  ,      G  �  )   csin        G  �  &      G  �  &      G  �  ,      G  �  &      G  �  ,      G  �  ,      G  �  ,      G  �  )   ccos        G  �  &      G  �  &      G  �  ,      G  �  &      G  �  ,      G  �  ,      G  �  ,      G  �  )   ctan        G  �  &      G  �  &      G  �  ,      G  �  &      G  �  ,      G  �  ,      G  �  ,      G  
  )   cacos       G    &      G    &      G    ,      G    &      G    ,      G    ,      G    ,      G  +  )   casinh      G  ,  &      G  ,  &      G  ,  ,      G  -  &      G  -  ,      G  /  ,      G  0  ,      G  I  )   casin       G  J  &      G  J  &      G  J  ,      G  K  &      G  K  ,      G  M  ,      G  N  ,      G  g  )   cacosh      G  h  &      G  h  &      G  h  ,      G  i  &      G  i  ,      G  k  ,      G  l  ,      G  �  )   catanh      G  �  &      G  �  &      G  �  ,      G  �  &      G  �  ,      G  �  ,      G  �  ,      G  �  )   catan       G  �  &      G  �  &      G  �  ,      G  �  &      G  �  ,      G  �  ,      G  �  ,      G  �  )   __muldc3        G  �  &      G  �  &      G  �  ,      G  �  ,      G  �  )   __divdc3        G  �  &      G  �  &      G  �  ,      G  �  ,        _            b   @       +  b   c               @                          !                            !              !                 !                 !  K                        \            `      _      �      _      �         6               7        8  6               7     	   8  6     
          7        8  6               7     
   8  6               7        7        8  6               7        7        8  6               7        7        8  6               7        7        7        8  6                7     !   7     "   7     #   8  6     $          7     %   7     &   8  6     '          7     (   7     )   8  6     *          7     +   7     ,   8  6     -          7     .   7     /   8  6     0          7     1   7     2   8  6     3          7     4   7     5   8  6     6          7     7   7     8   8  6     9          7     :   7     ;   8  6     <          7     =   7     >   8  6     ?          7     @   7     A   8  6     B          7     C   7     D   8  6     E          7     F   7     G   8  6     H          7     I   7     J   8  6     L       K   7     M   7     N   7     O   7     P   7     Q   8  6     R       K   7     S   7     T   7     U   7     V   7     W   8  6     X         7     Y   �  Z   ;     [      |  \   ]   Y   =     ^   ]         |  `   a   Y   F  `   d   a   c   |  \   e   d   =     f   e         |  `   g   [     g      |  `   h   [   F  `   i   h   c   |  \   j   [   >  j   ^         |  \   k   i   >  k   f         9     l      [   |  `   m   [    m      �  l   8  6     n         7     o   �  p   ;     q      |  \   r   o   =     s   r         |  `   t   o   F  `   u   t   c   |  \   v   u   =     w   v         |  `   x   q     x      |  `   y   q   F  `   z   y   c   |  \   {   q   >  {   s         |  \   |   z   >  |   w         9     }      q   |  `   ~   q    ~      �  }   8  6              7     �   �  �   ;     �      |  \   �   �   =     �   �         |  `   �   �   F  `   �   �   c   |  \   �   �   =     �   �         |  `   �   �     �      |  `   �   �   F  `   �   �   c   |  \   �   �   >  �   �         |  \   �   �   >  �   �         9     �   
   �   |  `   �   �    �      �  �   8  6     �         7     �   �  �   ;     �      |  \   �   �   =     �   �         |  `   �   �   F  `   �   �   c   |  \   �   �   =     �   �         |  `   �   �     �      |  `   �   �   F  `   �   �   c   |  \   �   �   >  �   �         |  \   �   �   >  �   �         9     �      �   |  `   �   �    �      �  �   8  6     �         7     �   7     �   �  �   ;     �      ;     �      y     �   �   |  \   �   �   =     �   �         |  `   �   �   F  `   �   �   c   |  \   �   �   =     �   �         |  `   �   �     �      |  `   �   �   F  `   �   �   c   |  \   �   �   >  �   �         |  \   �   �   >  �   �         9     �      �   �   |  \   �   �   =     �   �         |  `   �   �   F  `   �   �   c   |  \   �   �   =     �   �         |  `   �   �    �      |  �   �   �   F  �   �   �   c   |  �   �   �   >  �   �         |  �   �   �   >  �   �         �  8  6     �         7     �   7     �   �  �   ;     �      ;     �      y     �   �   |  \   �   �   =     �   �         |  `   �   �   F  `   �   �   c   |  \   �   �   =     �   �         |  `   �   �     �      |  `   �   �   F  `   �   �   c   |  \   �   �   >  �   �         |  \   �   �   >  �   �         9     �      �   �   |  \   �   �   =     �   �         |  `   �   �   F  `   �   �   c   |  \   �   �   =     �   �         |  `   �   �    �      |  �   �   �   F  �   �   �   c   |  �   �   �   >  �   �         |  �   �   �   >  �   �         �  8  6     �         7     �   7     �   �  �   ;     �      ;     �      y     �   �   |  \   �   �   =     �   �         |  `   �   �   F  `   �   �   c   |  \   �   �   =     �   �         |  `   �   �     �      |  `   �   �   F  `   �   �   c   |  \   �   �   >  �   �         |  \   �   �   >  �   �         9     �      �   �   |  \   �   �   =     �   �         |  `   �   �   F  `   �   �   c   |  \   �   �   =     �   �         |  `   �   �    �      |  �   �   �   F  �   �   �   c   |  �   �   �   >  �   �         |  �   �   �   >  �   �         �  8  6     �         7     �   7     �   7        �    ;          ;          ;          y         |  \     �   =               |  `     �   F  `   	    c   |  \   
  	  =       
        |  \        =     
          |  `        F  `       c   |  \       =               |  `              |  `       F  `       c   |  \       >            |  \       >            |  `              |  `       F  `       c   |  \       >    
        |  \       >            9                |  \       =               |  `       F  `        c   |  \   !     =     "  !        |  `   #     #     |  `   $     $     |  �   %  �   F  �   &  %  c   |  �   '  �   >  '          |  �   (  &  >  (  "        �  8  6     )        7     *  7     +  7     ,  �  -  ;     .     y     /  .  9     0      /  +  ,  |  \   1  .  =     2  1        |  `   3  .  F  `   4  3  c   |  \   5  4  =     6  5        |  �   7  *  F  �   8  7  c   |  �   9  *  >  9  2        |  �   :  8  >  :  6        �  8  6     ;        7     <  7     =  �  >  ;     ?     ;     @     y     A  ?  |  \   B  =  =     C  B        |  `   D  =  F  `   E  D  c   |  \   F  E  =     G  F        |  `   H  @    H     |  `   I  @  F  `   J  I  c   |  \   K  @  >  K  C        |  \   L  J  >  L  G        9     M  $   A  @  |  \   N  ?  =     O  N        |  `   P  ?  F  `   Q  P  c   |  \   R  Q  =     S  R        |  `   T  @   T     |  �   U  <  F  �   V  U  c   |  �   W  <  >  W  O        |  �   X  V  >  X  S        �  8  6     Y        7     Z  7     [  �  \  ;     ]     ;     ^     y     _  ]  |  \   `  [  =     a  `        |  `   b  [  F  `   c  b  c   |  \   d  c  =     e  d        |  `   f  ^    f     |  `   g  ^  F  `   h  g  c   |  \   i  ^  >  i  a        |  \   j  h  >  j  e        9     k  '   _  ^  |  \   l  ]  =     m  l        |  `   n  ]  F  `   o  n  c   |  \   p  o  =     q  p        |  `   r  ^   r     |  �   s  Z  F  �   t  s  c   |  �   u  Z  >  u  m        |  �   v  t  >  v  q        �  8  6     w        7     x  7     y  �  z  ;     {     ;     |     y     }  {  |  \   ~  y  =       ~        |  `   �  y  F  `   �  �  c   |  \   �  �  =     �  �        |  `   �  |    �     |  `   �  |  F  `   �  �  c   |  \   �  |  >  �          |  \   �  �  >  �  �        9     �  *   }  |  |  \   �  {  =     �  �        |  `   �  {  F  `   �  �  c   |  \   �  �  =     �  �        |  `   �  |   �     |  �   �  x  F  �   �  �  c   |  �   �  x  >  �  �        |  �   �  �  >  �  �        �  8  6     �        7     �  7     �  �  �  ;     �     ;     �     y     �  �  |  \   �  �  =     �  �        |  `   �  �  F  `   �  �  c   |  \   �  �  =     �  �        |  `   �  �    �     |  `   �  �  F  `   �  �  c   |  \   �  �  >  �  �        |  \   �  �  >  �  �        9     �  -   �  �  |  \   �  �  =     �  �        |  `   �  �  F  `   �  �  c   |  \   �  �  =     �  �        |  `   �  �   �     |  �   �  �  F  �   �  �  c   |  �   �  �  >  �  �        |  �   �  �  >  �  �        �  8  6     �        7     �  7     �  �  �  ;     �     ;     �     y     �  �  |  \   �  �  =     �  �        |  `   �  �  F  `   �  �  c   |  \   �  �  =     �  �        |  `   �  �    �     |  `   �  �  F  `   �  �  c   |  \   �  �  >  �  �        |  \   �  �  >  �  �        9     �  0   �  �  |  \   �  �  =     �  �        |  `   �  �  F  `   �  �  c   |  \   �  �  =     �  �        |  `   �  �   �     |  �   �  �  F  �   �  �  c   |  �   �  �  >  �  �        |  �   �  �  >  �  �        �  8  6     �        7     �  7     �  �  �  ;     �     ;     �     y     �  �  |  \   �  �  =     �  �        |  `   �  �  F  `   �  �  c   |  \   �  �  =     �  �        |  `   �  �    �     |  `   �  �  F  `   �  �  c   |  \   �  �  >  �  �        |  \   �  �  >  �  �        9     �  3   �  �  |  \   �  �  =     �  �        |  `   �  �  F  `   �  �  c   |  \   �  �  =     �  �        |  `   �  �   �     |  �   �  �  F  �   �  �  c   |  �   �  �  >  �  �        |  �   �  �  >  �  �        �  8  6     �        7     �  7     �  �  �  ;     �     ;     �     y     �  �  |  \   �  �  =     �  �        |  `   �  �  F  `   �  �  c   |  \   �  �  =     �  �        |  `   �  �    �     |  `   �  �  F  `   �  �  c   |  \   �  �  >  �  �        |  \      �  >     �        9       6   �  �  |  \     �  =               |  `     �  F  `       c   |  \       =               |  `     �        |  �   	  �  F  �   
  	  c   |  �     �  >            |  �     
  >            �  8  6     
        7       7       �    ;          ;          y         |  \       =               |  `       F  `       c   |  \       =               |  `              |  `       F  `       c   |  \       >            |  \       >            9       9       |  \        =     !           |  `   "    F  `   #  "  c   |  \   $  #  =     %  $        |  `   &     &     |  �   '    F  �   (  '  c   |  �   )    >  )  !        |  �   *  (  >  *  %        �  8  6     +        7     ,  7     -  �  .  ;     /     ;     0     y     1  /  |  \   2  -  =     3  2        |  `   4  -  F  `   5  4  c   |  \   6  5  =     7  6        |  `   8  0    8     |  `   9  0  F  `   :  9  c   |  \   ;  0  >  ;  3        |  \   <  :  >  <  7        9     =  <   1  0  |  \   >  /  =     ?  >        |  `   @  /  F  `   A  @  c   |  \   B  A  =     C  B        |  `   D  0   D     |  �   E  ,  F  �   F  E  c   |  �   G  ,  >  G  ?        |  �   H  F  >  H  C        �  8  6     I        7     J  7     K  �  L  ;     M     ;     N     y     O  M  |  \   P  K  =     Q  P        |  `   R  K  F  `   S  R  c   |  \   T  S  =     U  T        |  `   V  N    V     |  `   W  N  F  `   X  W  c   |  \   Y  N  >  Y  Q        |  \   Z  X  >  Z  U        9     [  ?   O  N  |  \   \  M  =     ]  \        |  `   ^  M  F  `   _  ^  c   |  \   `  _  =     a  `        |  `   b  N   b     |  �   c  J  F  �   d  c  c   |  �   e  J  >  e  ]        |  �   f  d  >  f  a        �  8  6     g        7     h  7     i  �  j  ;     k     ;     l     y     m  k  |  \   n  i  =     o  n        |  `   p  i  F  `   q  p  c   |  \   r  q  =     s  r        |  `   t  l    t     |  `   u  l  F  `   v  u  c   |  \   w  l  >  w  o        |  \   x  v  >  x  s        9     y  B   m  l  |  \   z  k  =     {  z        |  `   |  k  F  `   }  |  c   |  \   ~  }  =       ~        |  `   �  l   �     |  �   �  h  F  �   �  �  c   |  �   �  h  >  �  {        |  �   �  �  >  �          �  8  6     �        7     �  7     �  �  �  ;     �     ;     �     y     �  �  |  \   �  �  =     �  �        |  `   �  �  F  `   �  �  c   |  \   �  �  =     �  �        |  `   �  �    �     |  `   �  �  F  `   �  �  c   |  \   �  �  >  �  �        |  \   �  �  >  �  �        9     �  E   �  �  |  \   �  �  =     �  �        |  `   �  �  F  `   �  �  c   |  \   �  �  =     �  �        |  `   �  �   �     |  �   �  �  F  �   �  �  c   |  �   �  �  >  �  �        |  �   �  �  >  �  �        �  8  6     �        7     �  7     �  �  �  ;     �     ;     �     y     �  �  |  \   �  �  =     �  �        |  `   �  �  F  `   �  �  c   |  \   �  �  =     �  �        |  `   �  �    �     |  `   �  �  F  `   �  �  c   |  \   �  �  >  �  �        |  \   �  �  >  �  �        9     �  H   �  �  |  \   �  �  =     �  �        |  `   �  �  F  `   �  �  c   |  \   �  �  =     �  �        |  `   �  �   �     |  �   �  �  F  �   �  �  c   |  �   �  �  >  �  �        |  �   �  �  >  �  �        �  8  6     �     K   7     �  7     �  7     �  7     �  7     �  �  �  ;     �     y     �  �  9 	    �  L   �  �  �  �  �  |  \   �  �  =     �  �        |  `   �  �  F  `   �  �  c   |  \   �  �  =     �  �        |  �   �  �  F  �   �  �  c   |  �   �  �  >  �  �        |  �   �  �  >  �  �        �  8  6     �     K   7     �  7     �  7     �  7     �  7     �  �  �  ;     �     y     �  �  9 	    �  R   �  �  �  �  �  |  \   �  �  =     �  �        |  `   �  �  F  `   �  �  c   |  \   �  �  =     �  �        |  �   �  �  F  �   �  �  c   |  �   �  �  >  �  �        |  �   �  �  >  �  �        �  8  