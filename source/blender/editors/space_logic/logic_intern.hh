/*
 * ***** BEGIN GPL LICENSE BLOCK *****
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301, USA.
 *
 * The Original Code is Copyright (C) 2009 Blender Foundation.
 * All rights reserved.
 *
 *
 * Contributor(s): Blender Foundation
 *
 * ***** END GPL LICENSE BLOCK *****
 */

/** \file blender/editors/space_logic/logic_intern.h
 *  \ingroup splogic
 */

#pragma once

/* internal exports only */
struct bContext;
struct ARegion;
struct ScrArea;
struct wmOperatorType;

/* space_logic.c */
struct ARegion *logic_has_buttons_region(ScrArea *sa);

/* logic_ops.c */

/* logic_buttons.c */
void LOGIC_OT_properties(wmOperatorType *ot);
void LOGIC_OT_links_cut(wmOperatorType *ot);

/* logic_window.c */
void logic_buttons(bContext *C, ARegion *region);
/* void make_unique_prop_names(bContext *C, char *str); */
