/* SPDX-FileCopyrightText: 2011-2022 Blender Foundation
 *
 * SPDX-License-Identifier: Apache-2.0 */

#include "stdcycles.h"

shader node_convert_from_color(color value_color = 0.0,
                               output string value_string = "",
                               output float value_float = 0.0,
                               output int value_int = 0,
                               output vector value_vector = vector(0.0, 0.0, 0.0),
                               output point value_point = point(0.0, 0.0, 0.0),
                               output normal value_normal = normal(0.0, 0.0, 0.0))
{
  value_float = value_color[0] * 0.2126 + value_color[1] * 0.7152 + value_color[2] * 0.0722;
  value_int = (int)(value_color[0] * 0.2126 + value_color[1] * 0.7152 + value_color[2] * 0.0722);
  value_vector = vector(value_color[0], value_color[1], value_color[2]);
  value_point = point(value_color[0], value_color[1], value_color[2]);
  value_normal = normal(value_color[0], value_color[1], value_color[2]);
}
