// Copyright (c) 2007, 2008 libmv authors.
//
// Permission is hereby granted, free of charge, to any person obtaining a copy
// of this software and associated documentation files (the "Software"), to
// deal in the Software without restriction, including without limitation the
// rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
// sell copies of the Software, and to permit persons to whom the Software is
// furnished to do so, subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included in
// all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
// IN THE SOFTWARE.

#include "libmv/multiview/triangulation.h"

#include "libmv/multiview/projection.h"
#include "libmv/numeric/numeric.h"

namespace libmv {

// HZ 12.2 pag.312
void TriangulateDLT(const Mat34& P1,
                    const Vec2& x1,
                    const Mat34& P2,
                    const Vec2& x2,
                    Vec4* X_homogeneous) {
  Mat4 design;
  for (int i = 0; i < 4; ++i) {
    design(0, i) = x1(0) * P1(2, i) - P1(0, i);
    design(1, i) = x1(1) * P1(2, i) - P1(1, i);
    design(2, i) = x2(0) * P2(2, i) - P2(0, i);
    design(3, i) = x2(1) * P2(2, i) - P2(1, i);
  }
  Nullspace(&design, X_homogeneous);
}

void TriangulateDLT(const Mat34& P1,
                    const Vec2& x1,
                    const Mat34& P2,
                    const Vec2& x2,
                    Vec3* X_euclidean) {
  Vec4 X_homogeneous;
  TriangulateDLT(P1, x1, P2, x2, &X_homogeneous);
  HomogeneousToEuclidean(X_homogeneous, X_euclidean);
}

}  // namespace libmv
